using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储类型初始化管理器
    /// 统一管理所有存储系统支持的类型，包括类型标识符映射、类型工厂和转换委托的初始化
    /// 优化性能：预加载所有支持的类型转换委托，避免运行时生成
    /// </summary>
    public static class StorageTypeInitializer
    {
        #region 私有字段

        private static bool _isInitialized = false;
        private static readonly object _initLock = new object();

        // 所有支持的类型列表
        private static readonly Type[] _supportedTypes;

        // 类型标识符映射
        private static readonly Dictionary<Type, string> _typeToIdentifier;
        private static readonly Dictionary<string, Type> _identifierToType;

        /// <summary>
        /// 工厂函数字典
        /// </summary>
        private static Dictionary<Type, Func<StorageTypeWrapper>> _typeFactories;

        // 类型转换委托缓存
        private static readonly ConcurrentDictionary<StorageTypeConverter.DeserializationDelagateKey, StorageTypeConverter.DeserializationTypeConvertDelegate> _converReflectionDelegates;

        // 类型反射工厂映射
        private static readonly ConcurrentDictionary<Type, Func<StorageTypeWrapper>> _typeReflectionFactories;

        #endregion

        #region 静态构造函数

        private static Dictionary<Type, Func<StorageTypeWrapper>> CreateTypeFactories()
        {
            var factories = new Dictionary<Type, Func<StorageTypeWrapper>>()
            {
                // 注册基础类型
                {typeof(int), () => new StorageTypeWrapper<int>()},
                {typeof(float), () => new StorageTypeWrapper<float>()},
                {typeof(bool), () => new StorageTypeWrapper<bool>()},
                {typeof(long), () => new StorageTypeWrapper<long>()},
                {typeof(double), () => new StorageTypeWrapper<double>()},
                {typeof(string), () => new StorageTypeWrapper<string>()},
                {typeof(byte), () => new StorageTypeWrapper<byte>()},
                {typeof(sbyte), () => new StorageTypeWrapper<sbyte>()},
                {typeof(short), () => new StorageTypeWrapper<short>()},
                {typeof(ushort), () => new StorageTypeWrapper<ushort>()},
                {typeof(uint), () => new StorageTypeWrapper<uint>()},
                {typeof(ulong), () => new StorageTypeWrapper<ulong>()},
                {typeof(decimal), () => new StorageTypeWrapper<decimal>()},
                {typeof(char), () => new StorageTypeWrapper<char>()},
                {typeof(DateTime), () => new StorageTypeWrapper<DateTime>()},
                {typeof(Guid), () => new StorageTypeWrapper<Guid>()},

                // 注册Unity类型
                {typeof(Vector2), () => new StorageTypeWrapper<Vector2>()},
                {typeof(Vector3), () => new StorageTypeWrapper<Vector3>()},
                {typeof(Vector4), () => new StorageTypeWrapper<Vector4>()},
                {typeof(Quaternion), () => new StorageTypeWrapper<Quaternion>()},
                {typeof(Color), () => new StorageTypeWrapper<Color>()},
                {typeof(Rect), () => new StorageTypeWrapper<Rect>()},
                {typeof(RectInt), () => new StorageTypeWrapper<RectInt>()},
                {typeof(Bounds), () => new StorageTypeWrapper<Bounds>()},
                {typeof(BoundsInt), () => new StorageTypeWrapper<BoundsInt>()},
                {typeof(Vector2Int), () => new StorageTypeWrapper<Vector2Int>()},
                {typeof(Vector3Int), () => new StorageTypeWrapper<Vector3Int>()},
                {typeof(Color32), () => new StorageTypeWrapper<Color32>()},
                {typeof(Matrix4x4), () => new StorageTypeWrapper<Matrix4x4>()},
                {typeof(LayerMask), () => new StorageTypeWrapper<LayerMask>()},

                // 注册数组类型
                {typeof(int[]), () => new StorageTypeWrapper<int[]>()},
                {typeof(float[]), () => new StorageTypeWrapper<float[]>()},
                {typeof(double[]), () => new StorageTypeWrapper<double[]>()},
                {typeof(bool[]), () => new StorageTypeWrapper<bool[]>()},
                {typeof(string[]), () => new StorageTypeWrapper<string[]>()},
                {typeof(long[]), () => new StorageTypeWrapper<long[]>()},
                {typeof(char[]), () => new StorageTypeWrapper<char[]>()},
                {typeof(byte[]), () => new StorageTypeWrapper<byte[]>()},
                {typeof(sbyte[]), () => new StorageTypeWrapper<sbyte[]>()},
                {typeof(short[]), () => new StorageTypeWrapper<short[]>()},
                {typeof(ushort[]), () => new StorageTypeWrapper<ushort[]>()},
                {typeof(uint[]), () => new StorageTypeWrapper<uint[]>()},
                {typeof(ulong[]), () => new StorageTypeWrapper<ulong[]>()},
                {typeof(decimal[]), () => new StorageTypeWrapper<decimal[]>()},
                {typeof(Vector2[]), () => new StorageTypeWrapper<Vector2[]>()},
                {typeof(Vector3[]), () => new StorageTypeWrapper<Vector3[]>()},
                {typeof(Vector4[]), () => new StorageTypeWrapper<Vector4[]>()},
                {typeof(Quaternion[]), () => new StorageTypeWrapper<Quaternion[]>()},
                {typeof(Color[]), () => new StorageTypeWrapper<Color[]>()},
                {typeof(Color32[]), () => new StorageTypeWrapper<Color32[]>()},
                {typeof(Rect[]), () => new StorageTypeWrapper<Rect[]>()},

                // List 集合类型
                {typeof(List<int>), () => new StorageTypeWrapper<List<int>>()},
                {typeof(List<float>), () => new StorageTypeWrapper<List<float>>()},
                {typeof(List<double>), () => new StorageTypeWrapper<List<double>>()},
                {typeof(List<bool>), () => new StorageTypeWrapper<List<bool>>()},
                {typeof(List<string>), () => new StorageTypeWrapper<List<string>>()},
                {typeof(List<long>), () => new StorageTypeWrapper<List<long>>()},
                {typeof(List<char>), () => new StorageTypeWrapper<List<char>>()},
                {typeof(List<byte>), () => new StorageTypeWrapper<List<byte>>()},
                {typeof(List<sbyte>), () => new StorageTypeWrapper<List<sbyte>>()},
                {typeof(List<short>), () => new StorageTypeWrapper<List<short>>()},
                {typeof(List<ushort>), () => new StorageTypeWrapper<List<ushort>>()},
                {typeof(List<uint>), () => new StorageTypeWrapper<List<uint>>()},
                {typeof(List<ulong>), () => new StorageTypeWrapper<List<ulong>>()},
                {typeof(List<decimal>), () => new StorageTypeWrapper<List<decimal>>()},
                {typeof(List<Vector2>), () => new StorageTypeWrapper<List<Vector2>>()},
                {typeof(List<Vector3>), () => new StorageTypeWrapper<List<Vector3>>()},
                {typeof(List<Vector4>), () => new StorageTypeWrapper<List<Vector4>>()},
                {typeof(List<Quaternion>), () => new StorageTypeWrapper<List<Quaternion>>()},
                {typeof(List<Color>), () => new StorageTypeWrapper<List<Color>>()},
                {typeof(List<Color32>), () => new StorageTypeWrapper<List<Color32>>()},

                // Dictionary 集合类型 (string键)
                {typeof(Dictionary<string, int>), () => new StorageTypeWrapper<Dictionary<string, int>>()},
                {typeof(Dictionary<string, float>), () => new StorageTypeWrapper<Dictionary<string, float>>()},
                {typeof(Dictionary<string, double>), () => new StorageTypeWrapper<Dictionary<string, double>>()},
                {typeof(Dictionary<string, bool>), () => new StorageTypeWrapper<Dictionary<string, bool>>()},
                {typeof(Dictionary<string, string>), () => new StorageTypeWrapper<Dictionary<string, string>>()},
                {typeof(Dictionary<string, long>), () => new StorageTypeWrapper<Dictionary<string, long>>()},
                {typeof(Dictionary<string, Vector2>), () => new StorageTypeWrapper<Dictionary<string, Vector2>>()},
                {typeof(Dictionary<string, Vector3>), () => new StorageTypeWrapper<Dictionary<string, Vector3>>()},
                {typeof(Dictionary<string, Vector4>), () => new StorageTypeWrapper<Dictionary<string, Vector4>>()},
                {typeof(Dictionary<string, Quaternion>), () => new StorageTypeWrapper<Dictionary<string, Quaternion>>()},
                {typeof(Dictionary<string, Color>), () => new StorageTypeWrapper<Dictionary<string, Color>>()},
                {typeof(Dictionary<string, Color32>), () => new StorageTypeWrapper<Dictionary<string, Color32>>()},

                // Dictionary 集合类型 (int键)
                {typeof(Dictionary<int, int>), () => new StorageTypeWrapper<Dictionary<int, int>>()},
                {typeof(Dictionary<int, float>), () => new StorageTypeWrapper<Dictionary<int, float>>()},
                {typeof(Dictionary<int, double>), () => new StorageTypeWrapper<Dictionary<int, double>>()},
                {typeof(Dictionary<int, bool>), () => new StorageTypeWrapper<Dictionary<int, bool>>()},
                {typeof(Dictionary<int, string>), () => new StorageTypeWrapper<Dictionary<int, string>>()},
                {typeof(Dictionary<int, Vector2>), () => new StorageTypeWrapper<Dictionary<int, Vector2>>()},
                {typeof(Dictionary<int, Vector3>), () => new StorageTypeWrapper<Dictionary<int, Vector3>>()},
                {typeof(Dictionary<int, Color>), () => new StorageTypeWrapper<Dictionary<int, Color>>()},
            };

            return factories;
        }

        /// <summary>
        /// 创建类型标识符映射
        /// </summary>
        /// <returns>类型到标识符的映射字典</returns>
        private static Dictionary<Type, string> CreateTypeIdentifierMapping()
        {
            var mapping = new Dictionary<Type, string>();

            // 基础类型（单字符标识符）
            mapping[typeof(int)] = "i";
            mapping[typeof(float)] = "f";
            mapping[typeof(double)] = "d";
            mapping[typeof(bool)] = "b";
            mapping[typeof(string)] = "s";
            mapping[typeof(long)] = "l";
            mapping[typeof(char)] = "c";
            mapping[typeof(byte)] = "y";
            mapping[typeof(sbyte)] = "Y";
            mapping[typeof(short)] = "h";
            mapping[typeof(ushort)] = "H";
            mapping[typeof(uint)] = "u";
            mapping[typeof(ulong)] = "U";
            mapping[typeof(decimal)] = "m";
            mapping[typeof(DateTime)] = "dt";
            mapping[typeof(Guid)] = "g";

            // Unity基础类型
            mapping[typeof(Vector2)] = "V2";
            mapping[typeof(Vector3)] = "V3";
            mapping[typeof(Vector4)] = "V4";
            mapping[typeof(Quaternion)] = "Q";
            mapping[typeof(Color)] = "C";
            mapping[typeof(Color32)] = "C32";
            mapping[typeof(Rect)] = "R";
            mapping[typeof(Bounds)] = "B";
            mapping[typeof(Matrix4x4)] = "M4";
            mapping[typeof(Vector2Int)] = "V2i";
            mapping[typeof(Vector3Int)] = "V3i";
            mapping[typeof(RectInt)] = "Ri";
            mapping[typeof(BoundsInt)] = "Bi";
            mapping[typeof(LayerMask)] = "LM";

            // 数组类型
            mapping[typeof(int[])] = "i[]";
            mapping[typeof(float[])] = "f[]";
            mapping[typeof(double[])] = "d[]";
            mapping[typeof(bool[])] = "b[]";
            mapping[typeof(string[])] = "s[]";
            mapping[typeof(long[])] = "l[]";
            mapping[typeof(char[])] = "c[]";
            mapping[typeof(byte[])] = "y[]";
            mapping[typeof(sbyte[])] = "Y[]";
            mapping[typeof(short[])] = "h[]";
            mapping[typeof(ushort[])] = "H[]";
            mapping[typeof(uint[])] = "u[]";
            mapping[typeof(ulong[])] = "U[]";
            mapping[typeof(decimal[])] = "m[]";
            mapping[typeof(Vector2[])] = "V2[]";
            mapping[typeof(Vector3[])] = "V3[]";
            mapping[typeof(Vector4[])] = "V4[]";
            mapping[typeof(Quaternion[])] = "Q[]";
            mapping[typeof(Color[])] = "C[]";
            mapping[typeof(Color32[])] = "C32[]";

            // List 集合类型
            mapping[typeof(List<int>)] = "Li";
            mapping[typeof(List<float>)] = "Lf";
            mapping[typeof(List<double>)] = "Ld";
            mapping[typeof(List<bool>)] = "Lb";
            mapping[typeof(List<string>)] = "Ls";
            mapping[typeof(List<long>)] = "Ll";
            mapping[typeof(List<char>)] = "Lc";
            mapping[typeof(List<byte>)] = "Ly";
            mapping[typeof(List<sbyte>)] = "LY";
            mapping[typeof(List<short>)] = "Lh";
            mapping[typeof(List<ushort>)] = "LH";
            mapping[typeof(List<uint>)] = "Lu";
            mapping[typeof(List<ulong>)] = "LU";
            mapping[typeof(List<decimal>)] = "Lm";
            mapping[typeof(List<Vector2>)] = "LV2";
            mapping[typeof(List<Vector3>)] = "LV3";
            mapping[typeof(List<Vector4>)] = "LV4";
            mapping[typeof(List<Quaternion>)] = "LQ";
            mapping[typeof(List<Color>)] = "LC";
            mapping[typeof(List<Color32>)] = "LC32";

            // Dictionary 集合类型 (string键)
            mapping[typeof(Dictionary<string, int>)] = "Dsi";
            mapping[typeof(Dictionary<string, float>)] = "Dsf";
            mapping[typeof(Dictionary<string, double>)] = "Dsd";
            mapping[typeof(Dictionary<string, bool>)] = "Dsb";
            mapping[typeof(Dictionary<string, string>)] = "Dss";
            mapping[typeof(Dictionary<string, long>)] = "Dsl";
            mapping[typeof(Dictionary<string, Vector2>)] = "DsV2";
            mapping[typeof(Dictionary<string, Vector3>)] = "DsV3";
            mapping[typeof(Dictionary<string, Vector4>)] = "DsV4";
            mapping[typeof(Dictionary<string, Quaternion>)] = "DsQ";
            mapping[typeof(Dictionary<string, Color>)] = "DsC";
            mapping[typeof(Dictionary<string, Color32>)] = "DsC32";

            // Dictionary 集合类型 (int键)
            mapping[typeof(Dictionary<int, int>)] = "Dii";
            mapping[typeof(Dictionary<int, float>)] = "Dif";
            mapping[typeof(Dictionary<int, double>)] = "Did";
            mapping[typeof(Dictionary<int, bool>)] = "Dib";
            mapping[typeof(Dictionary<int, string>)] = "Dis";
            mapping[typeof(Dictionary<int, Vector2>)] = "DiV2";
            mapping[typeof(Dictionary<int, Vector3>)] = "DiV3";
            mapping[typeof(Dictionary<int, Color>)] = "DiC";

            return mapping;
        }

        /// <summary>
        /// 静态构造函数，初始化所有类型定义
        /// </summary>
        static StorageTypeInitializer()
        {
            // 定义所有支持的类型
            _supportedTypes = new Type[]
            {
                // 基础类型
                typeof(int), typeof(float), typeof(double), typeof(bool), typeof(string),
                typeof(long), typeof(char), typeof(byte), typeof(sbyte), typeof(short),
                typeof(ushort), typeof(uint), typeof(ulong), typeof(decimal),
                typeof(DateTime), typeof(Guid),

                // Unity基础类型
                typeof(Vector2), typeof(Vector3), typeof(Vector4), typeof(Quaternion),
                typeof(Color), typeof(Color32), typeof(Rect), typeof(Bounds),
                typeof(Matrix4x4), typeof(Vector2Int), typeof(Vector3Int),
                typeof(RectInt), typeof(BoundsInt), typeof(LayerMask),

                // 数组类型
                typeof(int[]), typeof(float[]), typeof(double[]), typeof(bool[]), typeof(string[]),
                typeof(long[]), typeof(char[]), typeof(byte[]), typeof(sbyte[]), typeof(short[]),
                typeof(ushort[]), typeof(uint[]), typeof(ulong[]), typeof(decimal[]),
                typeof(Vector2[]), typeof(Vector3[]), typeof(Vector4[]), typeof(Quaternion[]),
                typeof(Color[]), typeof(Color32[]),

                // List 集合类型
                typeof(List<int>), typeof(List<float>), typeof(List<double>), typeof(List<bool>), typeof(List<string>),
                typeof(List<long>), typeof(List<char>), typeof(List<byte>), typeof(List<sbyte>), typeof(List<short>),
                typeof(List<ushort>), typeof(List<uint>), typeof(List<ulong>), typeof(List<decimal>),
                typeof(List<Vector2>), typeof(List<Vector3>), typeof(List<Vector4>), typeof(List<Quaternion>),
                typeof(List<Color>), typeof(List<Color32>),

                // Dictionary 集合类型 (常用的键类型)
                typeof(Dictionary<string, int>), typeof(Dictionary<string, float>), typeof(Dictionary<string, double>),
                typeof(Dictionary<string, bool>), typeof(Dictionary<string, string>), typeof(Dictionary<string, long>),
                typeof(Dictionary<string, Vector2>), typeof(Dictionary<string, Vector3>), typeof(Dictionary<string, Vector4>),
                typeof(Dictionary<string, Quaternion>), typeof(Dictionary<string, Color>), typeof(Dictionary<string, Color32>),

                typeof(Dictionary<int, int>), typeof(Dictionary<int, float>), typeof(Dictionary<int, double>),
                typeof(Dictionary<int, bool>), typeof(Dictionary<int, string>), typeof(Dictionary<int, Vector2>),
                typeof(Dictionary<int, Vector3>), typeof(Dictionary<int, Color>)
            };

            // 初始化类型标识符映射
            _typeToIdentifier = CreateTypeIdentifierMapping();
            _identifierToType = CreateIdentifierTypeMapping(_typeToIdentifier);

            // 初始化类型工厂映射
            _typeFactories = CreateTypeFactories();

            // 初始化转换委托缓存
            _converReflectionDelegates = new ConcurrentDictionary<StorageTypeConverter.DeserializationDelagateKey, StorageTypeConverter.DeserializationTypeConvertDelegate>();

            // 初始化类型反射工厂映射
            _typeReflectionFactories = new ConcurrentDictionary<Type, Func<StorageTypeWrapper>>();

            NLogger.Log("StorageTypeInitializer static initialization completed with {0} supported types", arg0: _supportedTypes.Length);
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 类型到标识符的映射
        /// </summary>
        public static IReadOnlyDictionary<Type, string> TypeToIdentifier => _typeToIdentifier;

        /// <summary>
        /// 标识符到类型的映射
        /// </summary>
        public static IReadOnlyDictionary<string, Type> IdentifierToType => _identifierToType;

        /// <summary>
        /// 类型工厂映射
        /// </summary>
        public static Dictionary<Type, Func<StorageTypeWrapper>> TypeFactories => _typeFactories;

        /// <summary>
        /// 转换委托缓存
        /// </summary>
        public static ConcurrentDictionary<StorageTypeConverter.DeserializationDelagateKey, StorageTypeConverter.DeserializationTypeConvertDelegate> ConverReflectionDelegates => _converReflectionDelegates;

        /// <summary>
        /// 类型反射工厂映射
        /// </summary>
        public static ConcurrentDictionary<Type, Func<StorageTypeWrapper>> TypeReflectionFactories => _typeReflectionFactories;

        /// <summary>
        /// 所有支持的类型
        /// </summary>
        public static IReadOnlyList<Type> SupportedTypes => _supportedTypes;

        #endregion

        #region 公共方法

        /// <summary>
        /// 检查类型是否被支持
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否被支持</returns>
        public static bool IsTypeSupported(Type type)
        {
            return _typeToIdentifier.ContainsKey(type);
        }

        /// <summary>
        /// 获取类型标识符
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型标识符，如果不支持则返回null</returns>
        public static string GetTypeIdentifier(Type type)
        {
            return _typeToIdentifier.TryGetValue(type, out string identifier) ? identifier : null;
        }

        /// <summary>
        /// 获取标识符对应的类型
        /// </summary>
        /// <param name="identifier">类型标识符</param>
        /// <returns>类型，如果不存在则返回null</returns>
        public static Type GetTypeFromIdentifier(string identifier)
        {
            return _identifierToType.TryGetValue(identifier, out Type type) ? type : null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建标识符到类型的映射
        /// </summary>
        /// <param name="typeToIdentifier">类型到标识符的映射</param>
        /// <returns>标识符到类型的映射字典</returns>
        private static Dictionary<string, Type> CreateIdentifierTypeMapping(Dictionary<Type, string> typeToIdentifier)
        {
            var mapping = new Dictionary<string, Type>();
            foreach (var kvp in typeToIdentifier)
            {
                mapping[kvp.Value] = kvp.Key;
            }
            return mapping;
        }

        #endregion
    }
}