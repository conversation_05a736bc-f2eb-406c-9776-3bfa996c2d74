2025-05-30 03:35:33.7622 [INFO] [StorageTypeInitializer]::.cctor(326) - StorageTypeInitializer static initialization completed with 90 supported types
2025-05-30 03:35:33.7819 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully with 90 supported types and 0 preloaded conversion delegates
2025-05-30 03:35:33.7819 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-05-30 03:35:33.7819 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-05-30 03:35:34.1482 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-05-30 03:35:34.1482 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
2025-05-30 03:35:34.2970 [INFO] [StorageInstance]::.c<PERSON>(72) - Storage instance created: Test_BasicTypes
2025-05-30 03:35:34.3064 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testInt, Type: Int32, Value: 42
2025-05-30 03:35:34.6429 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntNegative, Type: Int32, Value: -123
2025-05-30 03:35:34.6839 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntZero, Type: Int32, Value: 0
2025-05-30 03:35:34.7022 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntMax, Type: Int32, Value: 2147483647
2025-05-30 03:35:34.7237 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntMin, Type: Int32, Value: -2147483648
2025-05-30 03:35:34.7318 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloat, Type: Single, Value: 3.14
2025-05-30 03:35:34.7318 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatNegative, Type: Single, Value: -2.71
2025-05-30 03:35:34.7318 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatZero, Type: Single, Value: 0
2025-05-30 03:35:34.7467 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatMax, Type: Single, Value: 3.402823E+38
2025-05-30 03:35:34.7467 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatMin, Type: Single, Value: -3.402823E+38
2025-05-30 03:35:34.7576 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testDouble, Type: Double, Value: 3.14159265358979
2025-05-30 03:35:34.7576 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testDoubleNegative, Type: Double, Value: -2.71828182845905
2025-05-30 03:35:34.7576 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testBoolTrue, Type: Boolean, Value: True
2025-05-30 03:35:34.7763 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testBoolFalse, Type: Boolean, Value: False
2025-05-30 03:35:34.7763 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testString, Type: String, Value: Hello World
2025-05-30 03:35:34.7930 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringEmpty, Type: String, Value: 
2025-05-30 03:35:34.7930 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringSpecial, Type: String, Value: 特殊字符：中文，符号!@#$%^&*()
2025-05-30 03:35:34.7930 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringUnicode, Type: String, Value: Unicode: 🎮🚀💻
2025-05-30 03:35:34.8071 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringJson, Type: String, Value: {"key": "value"}
2025-05-30 03:35:34.8071 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testByte, Type: Byte, Value: 255
2025-05-30 03:35:34.8225 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testShort, Type: Int16, Value: 32767
2025-05-30 03:35:34.8225 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testLong, Type: Int64, Value: 9223372036854775807
2025-05-30 03:35:34.8410 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testChar, Type: Char, Value: A
2025-05-30 03:35:34.8410 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testDecimal, Type: Decimal, Value: 123.456789
2025-05-30 03:35:34.8574 [INFO] [StorageCache]::Clear(176) - Cleared 24 items from cache
2025-05-30 03:35:34.8574 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_BasicTypes
2025-05-30 03:35:34.9602 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_UnityTypes
2025-05-30 03:35:34.9642 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector2, Type: Vector2, Value: (1.50, 2.50)
2025-05-30 03:35:34.9642 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector3, Type: Vector3, Value: (1.00, 2.00, 3.00)
2025-05-30 03:35:34.9815 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector4, Type: Vector4, Value: (1.00, 2.00, 3.00, 4.00)
2025-05-30 03:35:34.9815 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector2Int, Type: Vector2Int, Value: (10, 20)
2025-05-30 03:35:35.0020 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector3Int, Type: Vector3Int, Value: (1, 2, 3)
2025-05-30 03:35:35.0114 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testQuaternion, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-05-30 03:35:35.0114 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testQuaternionIdentity, Type: Quaternion, Value: (0.00000, 0.00000, 0.00000, 1.00000)
2025-05-30 03:35:35.0256 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testColor, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-30 03:35:35.0256 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testColorCustom, Type: Color, Value: RGBA(0.500, 0.300, 0.800, 0.900)
2025-05-30 03:35:35.0256 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testColor32, Type: Color32, Value: RGBA(255, 128, 64, 255)
2025-05-30 03:35:35.0476 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testRect, Type: Rect, Value: (x:10.00, y:20.00, width:100.00, height:200.00)
2025-05-30 03:35:35.0571 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testRectInt, Type: RectInt, Value: (x:5, y:10, width:50, height:100)
2025-05-30 03:35:35.0571 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testBounds, Type: Bounds, Value: Center: (0.00, 0.00, 0.00), Extents: (0.50, 0.50, 0.50)
2025-05-30 03:35:35.0763 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testBoundsInt, Type: BoundsInt, Value: Position: (0, 0, 0), Size: (10, 10, 10)
2025-05-30 03:35:35.0763 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testMatrix, Type: Matrix4x4, Value: 1.00000	0.00000	0.00000	0.00000
0.00000	1.00000	0.00000	0.00000
0.00000	0.00000	1.00000	0.00000
0.00... (类型: Matrix4x4)
2025-05-30 03:35:35.0917 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testMatrixTRS, Type: Matrix4x4, Value: 2.00000	0.00000	0.00000	1.00000
0.00000	2.00000	0.00000	1.00000
0.00000	0.00000	2.00000	1.00000
0.00... (类型: Matrix4x4)
2025-05-30 03:35:35.0917 [INFO] [StorageCache]::Clear(176) - Cleared 16 items from cache
2025-05-30 03:35:35.0917 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_UnityTypes
2025-05-30 03:35:35.2003 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_CollectionTypes
2025-05-30 03:35:35.2003 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testIntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-05-30 03:35:35.2003 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testFloatArray, Type: Single[], Value: [1.1, 2.2, 3.3]
2025-05-30 03:35:35.2003 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringArray, Type: String[], Value: ["apple", "banana", "cherry"]
2025-05-30 03:35:35.2130 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testVector3Array, Type: Vector3[], Value: [(0.00, 0.00, 0.00), (1.00, 1.00, 1.00), (0.00, 1.00, 0.00)]
2025-05-30 03:35:35.2130 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testEmptyIntArray, Type: Int32[], Value: []
2025-05-30 03:35:35.2130 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testIntList, Type: List`1, Value: [10, 20, 30, 40, 50]
2025-05-30 03:35:35.2268 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringList, Type: List`1, Value: ["Hello", "World", "Unity"]
2025-05-30 03:35:35.2268 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testVector3List, Type: List`1, Value: [(0.00, 0.00, 1.00), (0.00, 0.00, -1.00), (-1.00, 0.00, 0.00), (1.00, 0.00, 0.00)]
2025-05-30 03:35:35.2268 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testEmptyIntList, Type: List`1, Value: []
2025-05-30 03:35:35.2420 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringIntDict, Type: Dictionary`2, Value: {"first": 1, "second": 2, "third": 3}
2025-05-30 03:35:35.2420 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testIntStringDict, Type: Dictionary`2, Value: {1: "one", 2: "two", 3: "three"}
2025-05-30 03:35:35.2420 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringVector3Dict, Type: Dictionary`2, Value: {"origin": (0.00, 0.00, 0.00), "up": (0.00, 1.00, 0.00), "forward": (0.00, 0.00, 1.00)}
2025-05-30 03:35:35.2592 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testEmptyStringIntDict, Type: Dictionary`2, Value: {}
2025-05-30 03:35:35.2592 [INFO] [StorageCache]::Clear(176) - Cleared 13 items from cache
2025-05-30 03:35:35.2592 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_CollectionTypes
2025-05-30 03:35:35.3688 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_CustomTypes
2025-05-30 03:35:35.3688 [INFO] [StorageInstance]::Set(103) - Instance [Test_CustomTypes] - Key: testSimpleClass, Type: SimpleTestClass, Value: Storage.Test.StorageSerializerComprehensiveTest+SimpleTestClass
2025-05-30 03:35:35.3830 [INFO] [StorageInstance]::Set(103) - Instance [Test_CustomTypes] - Key: testStruct, Type: TestStruct, Value: Storage.Test.StorageSerializerComprehensiveTest+TestStruct
2025-05-30 03:35:35.3830 [INFO] [StorageCache]::Clear(176) - Cleared 2 items from cache
2025-05-30 03:35:35.3830 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_CustomTypes
2025-05-30 03:35:35.4952 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_NestedTypes
2025-05-30 03:35:35.4952 [INFO] [StorageInstance]::Set(103) - Instance [Test_NestedTypes] - Key: testComplexObj, Type: ComplexTestClass, Value: Storage.Test.StorageSerializerComprehensiveTest+ComplexTestClass
2025-05-30 03:35:35.4952 [INFO] [StorageInstance]::Set(103) - Instance [Test_NestedTypes] - Key: testNestedLists, Type: List`1, Value: [System.Collections.Generic.List`1[System.Int32], System.Collections.Generic.List`1[System.Int32], System.Collections.Generic.List`1[System.Int32]]
2025-05-30 03:35:35.4952 [INFO] [StorageCache]::Clear(176) - Cleared 2 items from cache
2025-05-30 03:35:35.4952 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_NestedTypes
2025-05-30 03:35:35.6117 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_NullValues
2025-05-30 03:35:35.6117 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullString, Type: String, Value: null
2025-05-30 03:35:35.6117 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullObject, Type: SimpleTestClass, Value: null
2025-05-30 03:35:35.6198 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullArray, Type: Int32[], Value: null
2025-05-30 03:35:35.6198 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullList, Type: List`1, Value: null
2025-05-30 03:35:35.6198 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testArrayWithNulls, Type: String[], Value: ["first", null, "third", null]
2025-05-30 03:35:35.6198 [INFO] [StorageCache]::Clear(176) - Cleared 5 items from cache
2025-05-30 03:35:35.6198 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_NullValues
2025-05-30 03:35:35.7352 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_BoundaryConditions
2025-05-30 03:35:35.7352 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testLargeList, Type: List`1, Value: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9... (共 10000 项)]
2025-05-30 03:35:35.7352 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testLongString, Type: String, Value: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA... (长度: 10000)
2025-05-30 03:35:35.7430 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testFloatNaN, Type: Single, Value: NaN
2025-05-30 03:35:35.7430 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testFloatInfinity, Type: Single, Value: Infinity
2025-05-30 03:35:35.7430 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testFloatNegInfinity, Type: Single, Value: -Infinity
2025-05-30 03:35:35.7577 [ERROR] [StorageInstance]::Set(96) - Key cannot be null or empty
2025-05-30 03:35:35.7577 [INFO] [StorageCache]::Clear(176) - Cleared 5 items from cache
2025-05-30 03:35:35.7577 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_BoundaryConditions
2025-05-30 03:35:35.8668 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_ErrorHandling
2025-05-30 03:35:35.8688 [WARN] [StorageCache]::TryGet(134) - Key 'nonExistentKey' not found
2025-05-30 03:35:35.8688 [INFO] [StorageInstance]::Set(103) - Instance [Test_ErrorHandling] - Key: typeMismatchTest, Type: Int32, Value: 42
2025-05-30 03:35:35.8862 [INFO] [StorageCache]::Clear(176) - Cleared 1 items from cache
2025-05-30 03:35:35.8862 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_ErrorHandling
2025-05-30 03:35:35.9919 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-05-30 03:35:35.9930 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Object, Value: 12345
2025-05-30 03:35:35.9930 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistFloat, Type: Object, Value: 3.14159
2025-05-30 03:35:35.9930 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: Object, Value: 持久化测试字符串
2025-05-30 03:35:35.9930 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistBool, Type: Object, Value: True
2025-05-30 03:35:36.0107 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistVector3, Type: Object, Value: (1.50, 2.50, 3.50)
2025-05-30 03:35:36.0107 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistColor, Type: Object, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-05-30 03:35:36.0107 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistIntArray, Type: Object, Value: [1, 2, 3, 4, 5]
2025-05-30 03:35:36.0238 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistStringList, Type: Object, Value: ["apple", "banana", "cherry"]
2025-05-30 03:35:36.0238 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistDict, Type: Object, Value: [[one, 1], [two, 2], [three, 3]]
2025-05-30 03:35:36.0426 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 9 items
2025-05-30 03:35:36.0426 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-05-30 03:35:36.0892 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-05-30 03:35:36.0892 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-05-30 03:35:36.0892 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-05-30 03:35:36.0892 [INFO] [StorageCache]::Clear(176) - Cleared 9 items from cache
2025-05-30 03:35:36.0892 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-05-30 03:35:36.0892 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-05-30 03:35:44.5999 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-05-30 03:35:45.5498 [INFO] [StorageInstance]::LoadFromFileSync(442) - Instance [Test_PersistenceWrite] - Starting sync load operation
