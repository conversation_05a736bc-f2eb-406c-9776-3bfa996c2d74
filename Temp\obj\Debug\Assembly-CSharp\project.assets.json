{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SingularityGroup.HotReload.Runtime.Public": "1.0.0", "Tayx.Graphy": "1.0.0", "Tayx.Graphy.Customization": "1.0.0", "Tayx.Graphy.Editor": "1.0.0", "VFolders": "1.0.0", "VHierarchy": "1.0.0", "VInspector": "1.0.0", "VTabs": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}, "runtime": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}}, "Tayx.Graphy/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Tayx.Graphy.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.dll": {}}}, "Tayx.Graphy.Customization/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Tayx.Graphy": "1.0.0"}, "compile": {"bin/placeholder/Tayx.Graphy.Customization.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.Customization.dll": {}}}, "Tayx.Graphy.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Tayx.Graphy": "1.0.0"}, "compile": {"bin/placeholder/Tayx.Graphy.Editor.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.Editor.dll": {}}}, "VFolders/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/VFolders.dll": {}}, "runtime": {"bin/placeholder/VFolders.dll": {}}}, "VHierarchy/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/VHierarchy.dll": {}}, "runtime": {"bin/placeholder/VHierarchy.dll": {}}}, "VInspector/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/VInspector.dll": {}}, "runtime": {"bin/placeholder/VInspector.dll": {}}}, "VTabs/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/VTabs.dll": {}}, "runtime": {"bin/placeholder/VTabs.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "path": "SingularityGroup.HotReload.Runtime.Public.csproj", "msbuildProject": "SingularityGroup.HotReload.Runtime.Public.csproj"}, "Tayx.Graphy/1.0.0": {"type": "project", "path": "Tayx.Graphy.csproj", "msbuildProject": "Tayx.Graphy.csproj"}, "Tayx.Graphy.Customization/1.0.0": {"type": "project", "path": "Tayx.Graphy.Customization.csproj", "msbuildProject": "Tayx.Graphy.Customization.csproj"}, "Tayx.Graphy.Editor/1.0.0": {"type": "project", "path": "Tayx.Graphy.Editor.csproj", "msbuildProject": "Tayx.Graphy.Editor.csproj"}, "VFolders/1.0.0": {"type": "project", "path": "VFolders.csproj", "msbuildProject": "VFolders.csproj"}, "VHierarchy/1.0.0": {"type": "project", "path": "VHierarchy.csproj", "msbuildProject": "VHierarchy.csproj"}, "VInspector/1.0.0": {"type": "project", "path": "VInspector.csproj", "msbuildProject": "VInspector.csproj"}, "VTabs/1.0.0": {"type": "project", "path": "VTabs.csproj", "msbuildProject": "VTabs.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "SingularityGroup.HotReload.Runtime.Public >= 1.0.0", "Tayx.Graphy >= 1.0.0", "Tayx.Graphy.Customization >= 1.0.0", "Tayx.Graphy.Editor >= 1.0.0", "VFolders >= 1.0.0", "VHierarchy >= 1.0.0", "VInspector >= 1.0.0", "VTabs >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\DiceGame\\DGame\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "H:\\DiceGame\\DGame\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\DiceGame\\DGame\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"H:\\DiceGame\\DGame\\Assembly-CSharp-firstpass.csproj": {"projectPath": "H:\\DiceGame\\DGame\\Assembly-CSharp-firstpass.csproj"}, "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.Public.csproj": {"projectPath": "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.Public.csproj"}, "H:\\DiceGame\\DGame\\Tayx.Graphy.csproj": {"projectPath": "H:\\DiceGame\\DGame\\Tayx.Graphy.csproj"}, "H:\\DiceGame\\DGame\\Tayx.Graphy.Customization.csproj": {"projectPath": "H:\\DiceGame\\DGame\\Tayx.Graphy.Customization.csproj"}, "H:\\DiceGame\\DGame\\Tayx.Graphy.Editor.csproj": {"projectPath": "H:\\DiceGame\\DGame\\Tayx.Graphy.Editor.csproj"}, "H:\\DiceGame\\DGame\\VFolders.csproj": {"projectPath": "H:\\DiceGame\\DGame\\VFolders.csproj"}, "H:\\DiceGame\\DGame\\VHierarchy.csproj": {"projectPath": "H:\\DiceGame\\DGame\\VHierarchy.csproj"}, "H:\\DiceGame\\DGame\\VInspector.csproj": {"projectPath": "H:\\DiceGame\\DGame\\VInspector.csproj"}, "H:\\DiceGame\\DGame\\VTabs.csproj": {"projectPath": "H:\\DiceGame\\DGame\\VTabs.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}