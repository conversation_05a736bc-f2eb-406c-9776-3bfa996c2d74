{"version": 3, "targets": {".NETStandard,Version=v2.1": {"SingularityGroup.HotReload.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"SingularityGroup.HotReload.Runtime.Public": "1.0.0"}, "compile": {"bin/placeholder/SingularityGroup.HotReload.Runtime.dll": {}}, "runtime": {"bin/placeholder/SingularityGroup.HotReload.Runtime.dll": {}}}, "SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}, "runtime": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}}, "VInspector/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/VInspector.dll": {}}, "runtime": {"bin/placeholder/VInspector.dll": {}}}}}, "libraries": {"SingularityGroup.HotReload.Runtime/1.0.0": {"type": "project", "path": "SingularityGroup.HotReload.Runtime.csproj", "msbuildProject": "SingularityGroup.HotReload.Runtime.csproj"}, "SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "path": "SingularityGroup.HotReload.Runtime.Public.csproj", "msbuildProject": "SingularityGroup.HotReload.Runtime.Public.csproj"}, "VInspector/1.0.0": {"type": "project", "path": "VInspector.csproj", "msbuildProject": "VInspector.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["SingularityGroup.HotReload.Runtime >= 1.0.0", "SingularityGroup.HotReload.Runtime.Public >= 1.0.0", "VInspector >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Editor.csproj", "projectName": "SingularityGroup.HotReload.Editor", "projectPath": "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\DiceGame\\DGame\\Temp\\obj\\Debug\\SingularityGroup.HotReload.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.csproj": {"projectPath": "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.csproj"}, "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.Public.csproj": {"projectPath": "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.Public.csproj"}, "H:\\DiceGame\\DGame\\VInspector.csproj": {"projectPath": "H:\\DiceGame\\DGame\\VInspector.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}